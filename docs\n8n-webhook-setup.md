# N8N Webhook Setup Guide for Chat Widget

This guide explains how to set up an n8n webhook to work with the chat widget.

## Prerequisites

1. Access to n8n (either cloud or self-hosted)
2. Your chat widget implemented in your website

## Setting Up the Webhook in n8n

### 1. Create a New Workflow

1. Log in to your n8n dashboard
2. Click "Create new workflow"
3. Name it (e.g., "Chat Widget Handler")

### 2. Add Webhook Node

1. Click the "+" button to add a new node
2. Search for "Webhook"
3. Select "Webhook"
4. Configure the webhook node:
   ```json
   {
     "Authentication": "None",
     "Method": "POST",
     "Path": "chat-response",
     "Response Mode": "Last Node"
   }
   ```

### 3. Set Up Response Handler

You need to format the response according to what the chat widget expects. Here's how:

1. Add a "Set" node after the Webhook
2. Configure it to structure the response:
   ```json
   {
     "keepOnlySet": true,
     "values": {
       "message": "Your response message here",
       "success": true
     }
   }
   ```

### Example Workflow

Here's a basic example of how your n8n workflow might look:

```plaintext
[Webhook] → [Set] → [Response]
```

## Webhook Response Format

The chat widget expects responses in this format:

```json
{
  "message": "The response message to show in chat",
  "success": true
}
```

## Testing the Webhook

1. After setting up, n8n will provide a webhook URL like:
   ```
   https://your-n8n-instance.com/webhook/chat-response
   ```

2. Copy this URL and update your ChatWidget component:
   ```typescript
   const N8N_WEBHOOK_URL = 'your-webhook-url-here';
   ```

## Advanced Configuration

### 1. Adding Authentication

If you want to secure your webhook:

1. In the Webhook node settings, enable "Authentication"
2. Choose "Header Auth"
3. Set a header name (e.g., "x-api-key")
4. Set a value (your secret key)
5. Update your ChatWidget component to include the header:
   ```typescript
   const handleSendMessage = async () => {
     // ... existing code ...
     const response = await fetch(N8N_WEBHOOK_URL, {
       method: 'POST',
       headers: {
         'Content-Type': 'application/json',
         'x-api-key': 'your-secret-key'
       },
       body: JSON.stringify({
         message: inputMessage,
       }),
     });
     // ... rest of the code ...
   };
   ```

### 2. Adding AI Integration

To integrate with AI services:

1. After the Webhook node, add your AI service node (e.g., OpenAI)
2. Configure the AI node with your API key
3. Connect it to process the incoming message
4. Use the Set node to format the AI response

Example workflow with AI:
```plaintext
[Webhook] → [OpenAI] → [Set] → [Response]
```

### 3. Error Handling

Add error handling in your workflow:

1. Add an "Error Trigger" node
2. Connect it to a Set node configured like this:
   ```json
   {
     "keepOnlySet": true,
     "values": {
       "message": "Sorry, there was an error processing your request",
       "success": false
     }
   }
   ```

## Webhook Data Structure

### Request (From Chat Widget to n8n)

```json
{
  "message": "User's message here"
}
```

### Response (From n8n to Chat Widget)

```json
{
  "message": "Bot's response here",
  "success": true
}
```

## Common Issues and Solutions

1. **CORS Errors**
   - In the Webhook node, enable "Response Headers"
   - Add:
     ```json
     {
       "Access-Control-Allow-Origin": "*",
       "Access-Control-Allow-Methods": "POST",
       "Access-Control-Allow-Headers": "Content-Type"
     }
     ```

2. **Timeout Issues**
   - Set "Request Timeout" in the Webhook node to a higher value
   - Add a "Respond to Webhook" node early in the flow to acknowledge receipt

3. **Message Formatting**
   - Use a Function node to format messages:
     ```javascript
     return {
       json: {
         message: items[0].json.message.trim(),
         success: true
       }
     }
     ```

## Best Practices

1. Always validate input messages
2. Include error handling
3. Add request logging for debugging
4. Set up monitoring for webhook availability
5. Implement rate limiting if needed
6. Keep response times quick (<5 seconds)
7. Include proper error messages in responses

## Testing Your Webhook

You can test your webhook using cURL:

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"message":"Hello"}' \
  https://your-n8n-instance.com/webhook/chat-response
```

Expected response:
```json
{
  "message": "Response to Hello",
  "success": true
}
```