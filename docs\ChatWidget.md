# Chat Widget Documentation

This documentation explains the ChatWidget component and how to customize it.

## Overview

The ChatWidget is a React component that creates a floating chat interface on the bottom-right corner of your website. It integrates with n8n for message handling.

## Features

- Floating chat button with smooth animations
- Expandable chat window
- Real-time message handling
- Integration with n8n webhook
- Loading states and error handling
- Automatic scroll to bottom on new messages
- Responsive design
- Dark theme using color code #162B4B

## Component Structure

1. **State Management:**
   ```typescript
   const [isOpen, setIsOpen] = useState(false);             // Controls chat window visibility
   const [messages, setMessages] = useState<Message[]>([]);  // Stores chat messages
   const [inputMessage, setInputMessage] = useState('');     // Handles input field
   const [isLoading, setIsLoading] = useState(false);       // Controls loading state
   ```

2. **Message Interface:**
   ```typescript
   interface Message {
     text: string;                // Message content
     sender: 'user' | 'bot';      // Message sender type
     timestamp: Date;             // Message timestamp
   }
   ```

3. **Main UI Components:**
   - <PERSON><PERSON> (Always visible)
   - Chat Window (Visible when isOpen is true)
   - Message Container
   - Input Area

## Customization Guide

1. **N8N Webhook URL:**
   Replace the placeholder URL with your n8n webhook:
   ```typescript
   const N8N_WEBHOOK_URL = 'YOUR_N8N_WEBHOOK_URL';
   ```

2. **Logo:**
   Update the logo path in the header:
   ```typescript
   <img
     src="/your-logo.png"
     alt="Ray White Ltd Logo"
     className="w-8 h-8 rounded-full"
   />
   ```

3. **Colors:**
   The main color scheme uses #162B4B. You can modify this by changing:
   ```typescript
   className="... bg-[#162B4B] ..."
   ```

4. **Company Name:**
   Change the company name in the header:
   ```typescript
   <span className="text-white font-semibold">Ray White Ltd.</span>
   ```

5. **Window Size:**
   Modify the chat window dimensions by changing:
   ```typescript
   className="... w-96 h-[500px] ..."
   ```

## N8N Integration

The chat widget expects the n8n webhook to:

1. Accept POST requests
2. Receive JSON data with format:
   ```json
   {
     "message": "user message here"
   }
   ```
3. Return JSON response with format:
   ```json
   {
     "message": "bot response here"
   }
   ```

## Error Handling

The widget includes error handling for:
- Failed API calls
- Empty messages
- Network issues

Error messages are displayed in the chat interface to maintain user experience.

## Adding to Your Project

1. Import the component:
   ```typescript
   import ChatWidget from './components/ChatWidget';
   ```

2. Add to your layout:
   ```typescript
   export default function Layout() {
     return (
       <>
         {/* Your other components */}
         <ChatWidget />
       </>
     );
   }
   ```

## Styling

The component uses Tailwind CSS for styling. Main style classes:

- Fixed positioning: `fixed bottom-6 right-6`
- Chat button: `w-14 h-14 rounded-full`
- Chat window: `w-96 h-[500px]`
- Primary color: `bg-[#162B4B]`
- Message bubbles: User messages use primary color, bot messages use gray

## Best Practices

1. Keep the N8N_WEBHOOK_URL in an environment variable
2. Test webhook responses before deployment
3. Implement rate limiting if needed
4. Add error boundaries for production use
5. Consider adding typing indicators for better UX