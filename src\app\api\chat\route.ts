import { NextRequest, NextResponse } from 'next/server';

const N8N_WEBHOOK_URL = 'https://raywhiteltd.app.n8n.cloud/webhook-test/website-234612121212';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    if (!body.message || typeof body.message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    // Forward the request to n8n webhook
    const response = await fetch(N8N_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: body.message,
      }),
    });

    if (!response.ok) {
      console.error('n8n webhook error:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to send message to webhook' },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    return NextResponse.json({
      message: data.message || 'Thank you for your message. I will get back to you soon.',
    });
  } catch (error) {
    console.error('API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
